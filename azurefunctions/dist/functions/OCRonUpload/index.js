"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const storage_blob_1 = require("@azure/storage-blob");
const ai_document_intelligence_1 = __importDefault(require("@azure-rest/ai-document-intelligence"));
const core_auth_1 = require("@azure/core-auth");
const AZ_CONN = process.env.AzureWebJobsStorage;
const SRC_CONTAINER = process.env.SRC_CONTAINER || "trial"; // 原始檔容器（使用者操作）
const MD_CONTAINER = process.env.DEST_CONTAINER || "markdown"; // OCR 結果容器（內部用）
const DOCINTEL_ENDPOINT = process.env.DOCINTEL_ENDPOINT;
const DOCINTEL_KEY = process.env.DOCINTEL_KEY;
const DOCINTEL_MODEL = process.env.DOCINTEL_MODEL || "prebuilt-layout"; // v4 模型；支援 images/PDF/（部分區域 API 亦支援 Office *x）
const OUTPUT_FORMAT = process.env.DOCINTEL_OUTPUT || "markdown"; // 直接要求 Markdown
const ACCOUNT_NAME = /AccountName=([^;]+)/.exec(AZ_CONN)?.[1];
const ACCOUNT_KEY = /AccountKey=([^;]+)/.exec(AZ_CONN)?.[1];
const sharedKey = new storage_blob_1.StorageSharedKeyCredential(ACCOUNT_NAME, ACCOUNT_KEY);
const svc = storage_blob_1.BlobServiceClient.fromConnectionString(AZ_CONN);
// ✅ 與 DI 服務連線：改為單例，避免每次重建（可減少冷啟延遲 / 連線數）
const di = (0, ai_document_intelligence_1.default)(DOCINTEL_ENDPOINT, new core_auth_1.AzureKeyCredential(DOCINTEL_KEY));
// 接受：PDF / PPTX / JPEG / PNG / TIFF；舊 .ppt 不支援 → 跳過並記錄
const ALLOWED = new Set([".pdf", ".pptx", ".jpg", ".jpeg", ".png", ".tif", ".tiff"]);
function hasAllowedExt(name) {
    const m = /\.[^.]+$/i.exec(name);
    const ext = m?.[0].toLowerCase();
    if (ext === ".ppt")
        return false; // legacy PPT 非 DI 支援輸入
    return !!ext && ALLOWED.has(ext);
}
function toMdName(srcName) { return srcName.replace(/\.[^.]+$/i, ".md"); }
// Metadata header values must be ASCII-only HTTP header-safe. If the source file name
// contains non-ASCII (e.g., 中文/emoji) or control chars, Node/HTTP will throw
// "Invalid character in header content". Encode non-ASCII and strip CTLs.
function sanitizeMetaValue(v) {
    const cleaned = String(v).replace(/[\x00-\x1F\x7F]/g, ""); // drop CTLs (including CR/LF)
    return /[^ -~]/.test(cleaned) ? encodeURIComponent(cleaned) : cleaned; // percent-encode non-ASCII
}
function buildReadOnlySasUrl(container, blobName, minutes = 60) {
    const blobClient = svc.getContainerClient(container).getBlobClient(blobName);
    const sas = (0, storage_blob_1.generateBlobSASQueryParameters)({
        containerName: container,
        blobName,
        permissions: storage_blob_1.BlobSASPermissions.parse("r"),
        startsOn: new Date(Date.now() - 2 * 60 * 1000),
        expiresOn: new Date(Date.now() + minutes * 60 * 1000),
        protocol: storage_blob_1.SASProtocol.Https,
    }, sharedKey).toString();
    return `${blobClient.url}?${sas}`;
}
// DISABLED: Commented out to prevent dual processing with the new Event Grid → Queue → Worker pattern
// To re-enable, uncomment the lines below and ensure OCRDispatcher is disabled
/*
app.storageBlob("OCRonUpload", {
  path: `${SRC_CONTAINER}/{name}`,
  connection: "AzureWebJobsStorage",
  source: "EventGrid",
  // dataType 預設 binary；此函式僅需 blob 名稱，因此不使用 _blob 內容
  handler: async (_blob: unknown, context) => {
    const name = String((context.triggerMetadata as Record<string, unknown> | undefined)?.name ?? "");
    context.log(`[INFO] Processing OCR request for file: ${name}`);

    if (!name || name.trim() === '') {
      context.log(`[ERROR] No file name provided in trigger metadata`);
      throw new Error("Invalid file name in trigger metadata");
    }

    if (!hasAllowedExt(name)) {
      context.log(`[WARN] Skipping unsupported file type: ${name}`);
      return;
    }

    // 1) 確保目標容器存在
    try {
      await svc.getContainerClient(MD_CONTAINER).createIfNotExists();
    } catch (containerError) {
      context.log(`[ERROR] Failed to ensure markdown container exists: ${MD_CONTAINER}`, containerError);
      throw new Error(`Container setup failed: ${containerError instanceof Error ? containerError.message : 'Unknown error'}`);
    }

    // 2) 以唯讀 SAS 讓 DI 讀取來源（TTL 提升至 60 分鐘，避免長檔案 polling 逾時）
    const srcSasUrl = buildReadOnlySasUrl(SRC_CONTAINER, name, 60);

    // 3) 呼叫 Document Intelligence（要求 Markdown 輸出）
    const initial = await di
      .path("/documentModels/{modelId}:analyze", DOCINTEL_MODEL)
      .post({
        contentType: "application/json",
        queryParameters: { outputContentFormat: OUTPUT_FORMAT },
        body: { urlSource: srcSasUrl },
      });

    if (isUnexpected(initial)) {
      const error = (initial as any).body?.error;
      context.log(`[ERROR] Document Intelligence analyze failed: status=${initial.status}, code=${error?.code}, message=${error?.message}`, error?.innererror);
      throw new Error(`Document Intelligence analyze failed: ${error?.message || initial.status}`);
    }

    const poller = getLongRunningPoller(di, initial);
    const result = await poller.pollUntilDone();

    if (isUnexpected(result)) {
      const error = (result as any).body?.error;
      context.log(`[ERROR] Document Intelligence polling failed: status=${result.status}, code=${error?.code}, message=${error?.message}`, error?.innererror);
      throw new Error(`Document Intelligence polling failed: ${error?.message || result.status}`);
    }

    const analyzeResult = result.body as AnalyzeOperationOutput;
    if (analyzeResult.status !== "succeeded") {
      context.log(`[ERROR] Document Intelligence analysis not succeeded: status=${analyzeResult.status}`, (analyzeResult as any).error);
      throw new Error(`Document Intelligence analysis failed with status: ${analyzeResult.status}`);
    }

    const format = (analyzeResult as any).analyzeResult?.contentFormat || "text";
    const markdown: string = (analyzeResult as any).analyzeResult?.content ?? "";

    if (!markdown.trim()) {
      context.log(`[WARN] No content returned from Document Intelligence; skipping write.`);
      return;
    }

    // 4) 寫回 markdown 容器（同路徑同檔名，改 .md）
    const dstName = toMdName(name);
    const dst = svc.getContainerClient(MD_CONTAINER).getBlockBlobClient(dstName);

    try {
      const markdownBuffer = Buffer.from(markdown, "utf8");
      await dst.upload(markdownBuffer, markdownBuffer.length, {
        blobHTTPHeaders: {
          blobContentType: "text/markdown; charset=utf-8",
          blobCacheControl: "no-cache",
        },
        metadata: {
          sourceFile: sanitizeMetaValue(name),
          processedAt: new Date().toISOString(),
          docIntelModel: DOCINTEL_MODEL,
          contentFormat: String(format),
        },
      });

      context.log(`[INFO] OCR→MD completed: ${SRC_CONTAINER}/${name} -> ${MD_CONTAINER}/${dstName} (${markdownBuffer.length} bytes, format=${format})`);
    } catch (uploadError) {
      context.log(`[ERROR] Failed to upload markdown to blob storage: source=${name}, destination=${dstName}`, uploadError);
      throw new Error(`Failed to upload processed markdown: ${uploadError instanceof Error ? uploadError.message : 'Unknown error'}`);
    }
  },
});
*/ 
