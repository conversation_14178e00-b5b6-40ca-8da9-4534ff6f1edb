# Azure Functions OCR Pipeline Optimizations

This document describes the optimizations applied to the Azure Functions OCR pipeline based on the patch recommendations.

## Overview

The OCR pipeline has been optimized to provide:
- Better reliability through proper retry logic and backoff
- Improved performance through controlled concurrency
- Enhanced monitoring through better metadata and logging
- Reduced duplicate processing through deduplication
- More efficient resource utilization

## Architecture Changes

### Event Grid → Queue → Worker Pattern

The pipeline now uses a reliable Event Grid → Queue → Worker pattern instead of direct blob triggers:

1. **Event Grid** receives blob creation events
2. **OCRDispatcher** filters and queues valid OCR jobs
3. **OCRWorker** processes jobs from the queue with proper retry logic

### Deprecated Components

- **OCRonUpload**: Disabled to prevent dual processing. The function is commented out but can be re-enabled if needed.

## Environment Variables

### New Configuration Options

| Variable | Default | Description |
|----------|---------|-------------|
| `OCR_ACCEPTED_EXTS` | `pdf,pptx,jpg,jpeg,png,tif,tiff` | Comma-separated list of allowed file extensions |
| `DI_POST_MAX_CONCURRENT` | `12` | Maximum concurrent POST requests to Document Intelligence (≤12 recommended) |
| `DI_POLL_BASE_DELAY_MS` | `2000` | Base polling delay in milliseconds (minimum 2000ms) |

### Existing Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `OCR_QUEUE_NAME` | `ocr-jobs` | Name of the storage queue for OCR jobs |
| `DI_ENDPOINT` | Required | Document Intelligence service endpoint |
| `DI_KEY` | Required | Document Intelligence service key |
| `DEST_CONTAINER` | `markdown` | Container for processed markdown files |

## Key Optimizations

### 1. Document Intelligence API v4 Integration

- **API Version**: Updated to `2024-02-29` (recommended stable version)
- **Markdown Output**: Direct markdown output using `outputContentFormat=markdown`
- **Enhanced Content Extraction**: Improved content extraction with fallback patterns

### 2. Controlled Concurrency and Rate Limiting

- **POST Concurrency**: Limited to ≤12 concurrent POST requests to respect DI service limits
- **Polling Frequency**: Minimum 2-second intervals between polls
- **Retry-After Respect**: Honors `Retry-After` headers from the service

### 3. Enhanced Retry Logic and Backoff

- **Exponential Backoff**: Official DI recommended sequence: 2→5→13→34 seconds
- **Status Code Handling**: Retries on 429 (rate limit) and 5xx (server errors)
- **Timeout Management**: 9-minute timeout to stay within function limits

### 4. Deduplication and Idempotency

- **Source ETag Tracking**: Prevents reprocessing of unchanged files
- **Enhanced Key Generation**: Improved deduplication key using multiple event properties
- **Blob Index Tags**: Uses Blob Index Tags for efficient metadata queries

### 5. Configurable File Filtering

- **Environment-Based**: File extensions configurable via `OCR_ACCEPTED_EXTS`
- **Centralized Logic**: Single point of configuration for supported file types
- **Enhanced Logging**: Better logging for filtered files

### 6. Improved Error Handling

- **Dead Letter Queue**: Failed jobs sent to DLQ with detailed error information
- **Structured Errors**: Enhanced error messages with context
- **Graceful Degradation**: Proper fallback handling for various failure scenarios

## Monitoring and Observability

### Enhanced Metadata

Processed files now include comprehensive metadata:
- Source file information (name, URL, ETag)
- Processing metrics (time, poll count)
- Model and format information
- Correlation IDs for tracing

### Blob Index Tags

- `sourceEtag`: For efficient deduplication queries
- Searchable and queryable metadata separate from blob content

### Logging Improvements

- Structured logging with correlation IDs
- Processing time tracking
- Enhanced error context
- Debug information for troubleshooting

## Performance Considerations

### Concurrency Limits

The system respects Document Intelligence service limits:
- **S0 Tier**: 15 TPS for POST requests, 50 TPS for GET requests
- **Function Configuration**: Defaults to 12 concurrent POST requests
- **Queue Processing**: Batch size of 16 with threshold of 8

### Memory and Timeout

- **Function Timeout**: 10 minutes (with 9-minute processing timeout)
- **Queue Visibility**: 2-minute visibility timeout
- **Polling Intervals**: 2-second maximum polling frequency

## Deployment Notes

### Required Changes

1. **Environment Variables**: Add new environment variables to Function App settings
2. **Event Grid Subscription**: Ensure Event Grid is configured to send to OCRDispatcher
3. **Queue Creation**: Ensure `ocr-jobs` and `ocr-jobs-deadletter` queues exist
4. **Monitoring**: Set up monitoring for the new metrics and logs

### Migration from OCRonUpload

1. Disable Event Grid subscription to OCRonUpload (if exists)
2. Enable Event Grid subscription to OCRDispatcher
3. Monitor both systems during transition period
4. Remove OCRonUpload subscription once new system is validated

## Troubleshooting

### Common Issues

1. **429 Rate Limiting**: Check DI_POST_MAX_CONCURRENT setting and reduce if needed
2. **Timeout Errors**: Verify DI_POLL_BASE_DELAY_MS is not too low
3. **Duplicate Processing**: Ensure OCRonUpload is disabled
4. **File Type Errors**: Check OCR_ACCEPTED_EXTS configuration

### Monitoring Points

- Queue depth and processing rate
- Document Intelligence API usage and quotas
- Function execution times and success rates
- Dead letter queue contents

## Future Enhancements

Potential future improvements based on the patch recommendations:
- Redis-based deduplication for multi-instance deployments
- Bulk SAS API for batch uploads
- Directory-level SAS for HNS-enabled storage
- Enhanced content deduplication using SHA-256 hashing
