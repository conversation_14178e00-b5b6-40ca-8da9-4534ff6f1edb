//azurefunctions/functions/OCRDispatcher/index.ts
import { app, EventGridEvent, InvocationContext } from '@azure/functions';
import { QueueClient } from '@azure/storage-queue';
import * as crypto from 'crypto';

const queue = new QueueClient(process.env.AZURE_STORAGE_CONNECTION_STRING!, process.env.OCR_QUEUE_NAME || 'ocr-jobs');
const dlq = new QueueClient(process.env.AZURE_STORAGE_CONNECTION_STRING!, (process.env.OCR_QUEUE_NAME || 'ocr-jobs') + '-deadletter');

// Configurable file extension filtering (patch optimization)
const OCR_ACCEPTED_EXTS = process.env.OCR_ACCEPTED_EXTS || 'pdf,pptx,jpg,jpeg,png,tif,tiff';
const allowedExtensions = new Set(OCR_ACCEPTED_EXTS.split(',').map(ext => ext.trim().toLowerCase().replace(/^\./, '')));

// simple in-memory dedupe cache (5 min). For production, use Redis/Table.
const seen = new Map<string, number>();
const DEDUPE_TTL_MS = 5 * 60 * 1000;

// Enhanced deduplication key generation (patch optimization)
function keyOf(e: any) {
  const id = e?.id || '';
  const api = e?.data?.api || '';
  const etag = e?.data?.eTag || '';
  const seq = e?.data?.sequencer || '';
  const subject = e?.subject || '';
  const eventTime = e?.eventTime || '';
  return crypto.createHash('sha1').update(`${id}|${api}|${etag}|${seq}|${subject}|${eventTime}`).digest('hex');
}

function keep(key: string) {
  const now = Date.now();
  for (const [k, ts] of Array.from(seen.entries())) if (now - ts > DEDUPE_TTL_MS) seen.delete(k);
  if (seen.has(key)) return false; seen.set(key, now); return true;
}

// Subscribed to Storage BlobCreated events from the `trial` container (subject filters recommended)
app.eventGrid('OCRDispatcher', {
  handler: async (event: EventGridEvent, context: InvocationContext): Promise<void> => {
    context.log(`[INFO] Processing Event Grid event: ${event.id}`);

    const type = event?.eventType as string;
    if (type !== 'Microsoft.Storage.BlobCreated') {
      context.log(`[DEBUG] Skipping event type: ${type}`);
      return; // ignore delete/soft-delete, etc.
    }

    const url = event?.data?.url as string | undefined;
    if (!url) {
      context.log(`[WARN] No URL in event data`);
      return;
    }
    
    // Enhanced file extension filtering with configurable extensions (patch optimization)
    const urlPath = new URL(url).pathname;
    const extension = urlPath.slice(urlPath.lastIndexOf('.') + 1).toLowerCase();
    if (!allowedExtensions.has(extension)) {
      context.log(`[DEBUG] Skipping non-supported file extension '${extension}': ${url}`);
      return; // configurable allowlist
    }

    const dedupeKey = keyOf(event);
    if (!keep(dedupeKey)) {
      context.log(`[DEBUG] Duplicate event detected, skipping: ${dedupeKey}`);
      return; // drop duplicates within TTL
    }

    // minimal payload to stay far below 64KB queue limit
    const payload = {
      url,
      eTag: event?.data?.eTag,
      sequencer: event?.data?.sequencer,
      correlationId: event?.id,
      subject: event?.subject,
      time: event?.eventTime,
    };

    // Enhanced queue operations with better error handling (patch optimization)
    try {
      const body = Buffer.from(JSON.stringify(payload)).toString('base64');
      await queue.sendMessage(body);
      context.log(`[INFO] Queued OCR job for: ${url} (correlation: ${payload.correlationId})`);
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : String(err);
      context.log(`[WARN] Enqueue failed for ${url}, sending to DLQ:`, errorMsg);

      try {
        const dlqPayload = {
          error: errorMsg,
          originalPayload: payload,
          failedAt: new Date().toISOString(),
          retryable: false // Mark as non-retryable since it failed at queue level
        };
        await dlq.sendMessage(Buffer.from(JSON.stringify(dlqPayload)).toString('base64'));
        context.log(`[INFO] Sent to DLQ: ${url}`);
      } catch (dlqErr) {
        const dlqErrorMsg = dlqErr instanceof Error ? dlqErr.message : String(dlqErr);
        context.log(`[ERROR] Failed to send to DLQ for ${url}:`, dlqErrorMsg);
        // Re-throw original error since we couldn't even queue to DLQ
        throw new Error(`Queue operation failed and DLQ fallback failed: ${errorMsg} | DLQ Error: ${dlqErrorMsg}`);
      }
    }
  }
});